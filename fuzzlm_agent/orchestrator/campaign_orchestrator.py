"""Campaign编排器 - 简化研究原型版
================================

实现docs/workflow.md中定义的核心工作流，采用简化架构设计。
直接函数调用，无事件总线，无状态机，同步优先。

主要职责：
- 管理Campaign完整生命周期（Phase 0-5）
- 内联实现简单phases（0, 2, 5）
- 调用独立模块处理复杂phases（1, 3, 4）
- 简单错误处理和日志记录
"""

from __future__ import annotations

import asyncio
import json
import logging
import os
import shutil
import traceback
from dataclasses import dataclass, field
from datetime import datetime, timezone
from pathlib import Path
from typing import Any

import yaml

from fuzzlm_agent.domain.schemas import CampaignResult
from fuzzlm_agent.infrastructure.id_generator import generate_campaign_id
from fuzzlm_agent.infrastructure.litellm_client import LiteLLMClient
from fuzzlm_agent.infrastructure.runtime_client import RuntimeClient
from fuzzlm_agent.infrastructure.shared_memory import TelemetryReader
from fuzzlm_agent.knowledge.simple_kb import SimpleKnowledgeBase


@dataclass
class CampaignContext:
    """贯穿整个Campaign的上下文数据"""

    campaign_id: str
    target_path: str
    duration_hours: float
    config: dict[str, Any] = field(default_factory=dict)

    # Phase产出
    workspace: str | None = None
    strategy: dict[str, Any] | None = None
    champion_id: str | None = None
    final_metrics: dict[str, Any] = field(default_factory=dict)
    reflection: str | None = None

    # 状态
    status: str = "running"  # running, completed, failed
    error: str | None = None
    start_time: datetime = field(default_factory=datetime.now)

    # 额外属性用于phase模块
    metadata: dict[str, Any] = field(default_factory=dict)
    elapsed_hours: float = 0.0
    needs_adjustment: bool = False

    def to_result(self) -> CampaignResult:
        """转换为CampaignResult对象"""
        return CampaignResult(
            campaign_id=self.campaign_id,
            success=self.status == "completed",
            start_time=self.start_time,
            end_time=datetime.now(),
            final_coverage=self.final_metrics.get("coverage", 0.0),
            unique_crashes=self.final_metrics.get("unique_crashes", 0),
            total_executions=self.final_metrics.get("total_executions", 0),
            strategy_changes=self.final_metrics.get("strategy_changes", 0),
            phases_completed=self.final_metrics.get("phases_completed", 0),
            error_message=self.error,
            metadata={
                "workspace": self.workspace,
                "reflection": self.reflection,
            },
        )


class CampaignOrchestrator:
    """极简的Campaign编排器 - 研究原型版"""

    def __init__(self, config_path: str = "config.yaml"):
        """初始化编排器

        Args:
            config_path: 配置文件路径

        """
        self.logger = logging.getLogger(__name__)

        # 加载配置
        self.config = self._load_config(config_path)

        # 初始化基础设施组件
        self.llm_client: LiteLLMClient | None = None
        self.runtime_client: RuntimeClient | None = None
        self.telemetry_reader: TelemetryReader | None = None
        self.knowledge_base: SimpleKnowledgeBase | None = None

        # 初始化组件
        self._initialize_components()

    def _load_config(self, config_path: str) -> dict[Any, Any]:
        """加载配置文件"""
        try:
            with open(config_path) as f:
                result = yaml.safe_load(f)
                return result if isinstance(result, dict) else {}
        except Exception as e:
            self.logger.warning(f"Failed to load config from {config_path}: {e}")
            # 返回默认配置
            return {
                "llm": {
                    "provider": "openrouter",
                    "model": "anthropic/claude-3.5-sonnet",
                    "api_key": os.getenv("OPENROUTER_API_KEY", ""),
                },
                "grpc": {"address": "localhost:50051"},
                "knowledge_base": {"path": "./data/knowledge_base.db"},
                "system": {
                    "workspace_root": "./workspace",
                    "default_duration_hours": 2.0,
                },
            }

    def _initialize_components(self) -> None:
        """初始化基础设施组件"""
        try:
            # 创建LLM客户端
            llm_config = self.config.get("llm", {})
            self.llm_client = LiteLLMClient(llm_config)

            # 创建运行时客户端
            grpc_config = self.config.get("grpc", {})
            self.runtime_client = RuntimeClient(
                {
                    "server_address": grpc_config.get("address", "localhost:50051"),
                    "timeout": grpc_config.get("timeout", 30.0),
                },
            )

            # 创建遥测读取器
            self.telemetry_reader = TelemetryReader("fuzzlm_telemetry")

            # 创建知识库
            kb_path = self.config.get("knowledge_base", {}).get(
                "path",
                "./data/knowledge_base.db",
            )
            self.knowledge_base = SimpleKnowledgeBase(db_path=kb_path)

            self.logger.info("Components initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize components: {e}")
            raise

    def run_campaign(self, target_path: str, hours: float = 2.0) -> CampaignResult:
        """同步接口运行Campaign

        Args:
            target_path: 目标程序路径
            hours: 运行时长（小时）

        Returns:
            CampaignResult: Campaign执行结果

        """
        # 使用asyncio.run处理异步
        return asyncio.run(self._run_async(target_path, hours))

    async def _run_async(self, target_path: str, hours: float) -> CampaignResult:
        """实际的异步执行逻辑"""
        # 创建Campaign上下文
        ctx = CampaignContext(
            campaign_id=generate_campaign_id(target_path, self.config),
            target_path=target_path,
            duration_hours=hours,
            config=self.config,
        )

        self.logger.info(f"Starting campaign {ctx.campaign_id} for {target_path}")

        try:
            # 连接到Rust运行时
            if self.runtime_client:
                await self.runtime_client.connect()
                # 检查连接状态
                health = await self.runtime_client.health_check()
                self.logger.info(f"Runtime client health: {health}")

            # 连接遥测读取器
            if self.telemetry_reader:
                connected = await self.telemetry_reader.connect()
                if connected:
                    self.logger.info("Telemetry reader connected")
                else:
                    self.logger.warning("Failed to connect telemetry reader")

            # Phase 0: 系统初始化
            ctx = self._phase0_init(ctx)
            ctx.final_metrics["phases_completed"] = 1

            # Phase 1: 策略生成（异步，独立模块）
            from .phase1_strategy import phase1_generate_strategy

            # 确保 llm_client 不为 None
            if self.llm_client is None:
                msg = "LLM client is not initialized"
                raise RuntimeError(msg)

            ctx = await phase1_generate_strategy(
                ctx,
                self.llm_client,
                self.knowledge_base,
            )
            ctx.final_metrics["phases_completed"] = 2
            ctx.final_metrics["strategy_changes"] = 0  # Initialize counter

            # Phase 2: 验证
            ctx = self._phase2_validate(ctx)
            ctx.final_metrics["phases_completed"] = 3

            # Phase 3: 生产运行（异步，独立模块）
            from .phase3_production import phase3_production_run

            # 检查 runtime_client（llm_client 已在前面检查过）
            if self.runtime_client is None:
                msg = "Runtime client not initialized"
                raise RuntimeError(msg)

            ctx = await phase3_production_run(
                ctx,
                self.runtime_client,  # runtime_client
                self.llm_client,  # llm_client
                self.telemetry_reader,  # telemetry_reader (now real)
                hours,  # duration_hours
            )
            ctx.final_metrics["phases_completed"] = 4

            # Phase 4: Shadow testing is now integrated within Phase 3
            # Dynamic shadow processes are created and managed during Phase 3 runtime
            # as per the research design in workflow.md
            ctx.final_metrics["phases_completed"] = 5

            # Phase 5: 反思学习
            ctx = await self._phase5_reflect(ctx)
            ctx.final_metrics["phases_completed"] = 6

            # 标记完成
            ctx.status = "completed"
            self.logger.info(f"Campaign {ctx.campaign_id} completed successfully")

        except Exception as e:
            ctx.status = "failed"
            ctx.error = str(e)
            self.logger.error(f"Campaign {ctx.campaign_id} failed: {e}")
            self.logger.error(traceback.format_exc())

        finally:
            # 断开连接
            if self.telemetry_reader:
                await self.telemetry_reader.disconnect()

            # 清理工作空间（可选）
            if ctx.workspace and self.config.get("system", {}).get(
                "cleanup_workspace",
                False,
            ):
                self._cleanup_workspace(ctx.workspace)

        return ctx.to_result()

    def _phase0_init(self, ctx: CampaignContext) -> CampaignContext:
        """Phase 0: 系统初始化 - 简单内联实现"""
        self.logger.info("Phase 0: System initialization")

        # 1. 验证目标文件
        target = Path(ctx.target_path)
        if not target.exists():
            msg = f"Target not found: {ctx.target_path}"
            raise FileNotFoundError(msg)

        if target.suffix not in [".c", ".cpp", ".cc"]:
            msg = f"Unsupported target type: {target.suffix}"
            raise ValueError(msg)

        # 2. 创建工作空间
        workspace_root = self.config.get("directories", {}).get(
            "workspace_dir",
            "./workspace",
        )
        ctx.workspace = f"{workspace_root}/{ctx.campaign_id}"
        os.makedirs(ctx.workspace, exist_ok=True)

        # 3. 复制目标到工作空间
        target_copy = Path(ctx.workspace) / "target" / target.name
        target_copy.parent.mkdir(exist_ok=True)
        shutil.copy2(ctx.target_path, target_copy)

        # 4. 创建种子目录
        seeds_dir = Path(ctx.workspace) / "seeds"
        seeds_dir.mkdir(exist_ok=True)

        # 5. 创建默认种子
        # TODO: 按目标类型从预设模板中选择或者调用LLM生成
        default_seeds = [
            b"",  # 空种子
            b"A",  # 单字符
            b"ABC",  # 短字符串
            b"1234567890",  # 数字
            b"\x00\x01\x02\x03",  # 二进制
            b"A" * 100,  # 重复模式
        ]

        for i, seed in enumerate(default_seeds):
            seed_path = seeds_dir / f"seed_{i:03d}"
            seed_path.write_bytes(seed)

        self.logger.info(
            f"Workspace created at {ctx.workspace} "
            f"with {len(default_seeds)} default seeds",
        )

        return ctx

    def _phase2_validate(self, ctx: CampaignContext) -> CampaignContext:
        """Phase 2: 策略验证 - 实现复合验证流程"""
        self.logger.info("Phase 2: Strategy validation with compound verification")

        if not ctx.strategy:
            msg = "No strategy generated in Phase 1"
            raise ValueError(msg)

        # 1. 基本结构验证
        self._validate_strategy_structure(ctx.strategy)

        # 2. 系统集成验证已简化 - 根据研究原型最小化需求
        # 原SystemIntegrationValidator已被移除，使用简化的验证逻辑
        # 直接进行基本的可行性检查，而非复杂的系统集成验证

        # 3. 如果有自定义代码，执行复合验证
        if "custom_code" in ctx.strategy:
            self._validate_custom_code(ctx)

        # 4. 验证策略配置的合理性
        self._validate_strategy_parameters(ctx.strategy)

        self.logger.info("Strategy validated successfully")

        return ctx

    def _validate_strategy_structure(self, strategy: dict[str, Any]) -> None:
        """验证策略的基本结构"""
        required_fields = ["mutators", "scheduler", "feedback"]
        for field_name in required_fields:
            if field_name not in strategy:
                msg = f"Strategy missing required field: {field_name}"
                raise ValueError(msg)

        # 验证mutators
        mutators = strategy.get("mutators", [])
        if not mutators:
            msg = "Strategy has no mutators defined"
            raise ValueError(msg)

        # 检查mutator类型
        valid_mutator_types = [
            "BitFlipMutator",
            "ByteFlipMutator",
            "HavocMutator",
            "TokenInsertMutator",
            "CrossoverMutator",
        ]

        for mutator in mutators:
            if isinstance(mutator, dict):
                mutator_type = mutator.get("type", "")
                if mutator_type not in valid_mutator_types:
                    self.logger.warning(f"Unknown mutator type: {mutator_type}")

    def _validate_custom_code(self, ctx: CampaignContext) -> None:
        """验证自定义代码（简化版）

        注意：研究原型中，我们只做基本验证。
        真实实现应该调用Rust运行时的验证沙箱。
        """
        if ctx.strategy is None:
            return
        custom_code = ctx.strategy.get("custom_code", "")

        if not custom_code:
            return

        self.logger.info("Validating custom code...")

        # 基本的安全检查
        dangerous_patterns = [
            "unsafe ",
            "std::process",
            "std::fs::remove",
            "panic!",
            "unreachable!",
            "unimplemented!",
        ]

        for pattern in dangerous_patterns:
            if pattern in custom_code:
                self.logger.warning(f"Potentially dangerous pattern found: {pattern}")

        # 在真实实现中的安全验证流程已简化
        # 当前研究原型使用基本的安全检查替代复杂的沙箱验证
        # 1. 调用runtime_client.validate_code()进行静态分析
        # 2. 在沙箱中编译和测试代码
        # 3. 执行动态行为验证

        # 对于研究原型，我们假设验证通过
        self.logger.info("Custom code validation completed (simplified)")

    def _validate_strategy_parameters(self, strategy: dict[str, Any]) -> None:
        # TODO: 没有意义的check，需要重构
        """验证策略参数的合理性"""
        # 检查mutator数量
        mutator_count = len(strategy.get("mutators", []))
        if mutator_count > 20:
            self.logger.warning(
                f"Strategy has {mutator_count} mutators, which may impact performance",
            )
        elif mutator_count < 3:
            self.logger.warning(
                f"Strategy has only {mutator_count} mutators, "
                f"which may limit exploration",
            )

        # 检查scheduler配置
        scheduler = strategy.get("scheduler", {})
        if isinstance(scheduler, dict):
            scheduler_type = scheduler.get("type", "queue")
            if scheduler_type not in ["queue", "weighted", "adaptive"]:
                self.logger.warning(f"Unknown scheduler type: {scheduler_type}")

        # 检查feedback配置
        feedback = strategy.get("feedback", {})
        if isinstance(feedback, dict):
            feedback_type = feedback.get("type", "coverage")
            if feedback_type not in ["coverage", "crash", "combined"]:
                self.logger.warning(f"Unknown feedback type: {feedback_type}")

    async def _phase5_reflect(self, ctx: CampaignContext) -> CampaignContext:
        """Phase 5: 反思学习 - 使用核心反思引擎"""
        self.logger.info("Phase 5: Reflective learning with core engine")

        try:
            # 使用简化的反思机制 - 符合研究原型的最小化需求
            # 原核心反思引擎已被移除，符合研究原型的最小化需求

            # 存储反思结果（简化版）
            ctx.reflection = json.dumps(
                {
                    "insights": ["Campaign completed successfully"],
                    "improvements": ["Consider optimizing strategy parameters"],
                    "generalizable_lessons": ["Basic fuzzing strategy worked"],
                    "state_awareness_adjustments": [],
                    "identified_patterns": [],
                    "confidence_score": 0.7,
                },
            )

            # 如果有知识库，存储经验
            if self.knowledge_base:
                await self._store_simple_experience(ctx)

            self.logger.info("Reflection completed (simplified version)")

        except Exception as e:
            self.logger.error(f"Reflection failed: {e}")
            # 降级处理：存储基本信息
            ctx.reflection = json.dumps(
                {"error": str(e), "basic_metrics": ctx.final_metrics},
            )

        return ctx

    async def _store_simple_experience(self, ctx: CampaignContext) -> None:
        """存储简化的经验到知识库"""
        try:
            # 简化的经验存储
            experience_data = {
                "campaign_id": ctx.campaign_id,
                "target": Path(ctx.target_path).name,
                "strategy": ctx.strategy or {},
                "metrics": ctx.final_metrics,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

            # 存储到知识库
            if self.knowledge_base is not None:
                await self.knowledge_base.store_simple_experience(
                    ctx.campaign_id,
                    experience_data,
                )

        except Exception as e:
            self.logger.error(f"Failed to store simple experience: {e}")

    def _cleanup_workspace(self, workspace: str) -> None:
        """清理工作空间"""
        try:
            shutil.rmtree(workspace)
            self.logger.info(f"Cleaned up workspace: {workspace}")
        except Exception as e:
            self.logger.warning(f"Failed to cleanup workspace: {e}")


# 用于命令行直接测试
if __name__ == "__main__":
    import sys

    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )

    # 检查参数
    if len(sys.argv) < 2:
        print("Usage: python campaign_orchestrator.py <target_path> [hours]")
        sys.exit(1)

    target_path = sys.argv[1]
    hours = float(sys.argv[2]) if len(sys.argv) > 2 else 2.0

    # 运行Campaign
    orchestrator = CampaignOrchestrator()
    result = orchestrator.run_campaign(target_path, hours)

    # 打印结果
    print("\nCampaign Result:")
    print(f"  Success: {result.success}")
    print(f"  Duration: {result.duration_hours:.2f} hours")
    print(f"  Coverage: {result.final_coverage:.2%}")
    print(f"  Crashes: {result.unique_crashes}")
    print(f"  Executions: {result.total_executions}")
    if result.error_message:
        print(f"  Error: {result.error_message}")
