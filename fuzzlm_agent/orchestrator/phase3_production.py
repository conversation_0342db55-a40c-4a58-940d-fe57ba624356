"""Phase 3: Production Run with Dynamic Shadow Process Support
=========================================================

SOTA implementation of Phase 3 production run using concurrent tasks for real-time
responsiveness and dynamic shadow process creation as per research requirements
in docs/workflow.md.

Architecture:
- Telemetry collection task: Continuous non-blocking data gathering
- LLM analysis task: Periodic state analysis every 10 minutes
- Anomaly detection task: Real-time anomaly monitoring with immediate response
- Shadow process manager: Dynamic shadow process creation and management

This is a research prototype focused on functional completeness while avoiding
over-engineering. Uses standard Python asyncio patterns for simplicity.
"""

from __future__ import annotations

import asyncio
import logging
from dataclasses import dataclass, field
from datetime import datetime, timedelta, timezone
from typing import TYPE_CHECKING

from fuzzlm_agent.core import (
    PerformanceMetrics,
    TelemetryAnalyzer,
)
from fuzzlm_agent.core.statistical_analysis import TelemetryAggregator
from fuzzlm_agent.orchestrator.shadow_process_manager import ShadowProcessManager
from fuzzlm_agent.prompts import PromptManager

if TYPE_CHECKING:
    from fuzzlm_agent.infrastructure.litellm_client import LiteLL<PERSON>lient
    from fuzzlm_agent.infrastructure.runtime_client import RuntimeClient
    from fuzzlm_agent.infrastructure.shared_memory import TelemetryReader
    from fuzzlm_agent.orchestrator.campaign_orchestrator import CampaignContext

logger = logging.getLogger(__name__)

# Initialize prompt manager (global instance for performance)
_prompt_manager = PromptManager()


@dataclass
class StateAnalysis:
    """LLM's analysis of fuzzing state."""

    timestamp: datetime
    current_stage: str  # exploration, exploitation, stagnation
    confidence: float
    bottlenecks: list[str] = field(default_factory=list)
    recommendations: list[str] = field(default_factory=list)
    needs_adjustment: bool = False
    suggested_actions: list[str] = field(default_factory=list)
    raw_response: str = ""


async def phase3_production_run(
    ctx: CampaignContext,
    runtime_client: RuntimeClient,
    llm_client: LiteLLMClient,
    telemetry_reader: TelemetryReader | None,
    duration_hours: float,
) -> CampaignContext:
    """Phase 3: Production run with adaptive state perception using concurrent tasks.

    Implements the real-time responsiveness requirements from workflow.md:
    - "每10分钟将收集到的近期原始数据流发送给LLM" (Send data to LLM every 10 minutes)
    - Real-time anomaly detection and response
    - Non-blocking telemetry collection

    Args:
        ctx: Campaign context with strategy
        runtime_client: Runtime client for fuzzer control
        llm_client: LLM client for state analysis
        telemetry_reader: Telemetry reader for monitoring
        duration_hours: Duration of production run in hours

    Returns:
        Updated campaign context with monitoring results
    """
    logger.info(
        f"Phase 3: Starting production run for {duration_hours} hours with concurrent architecture"
    )

    # Ensure metadata field exists
    if not hasattr(ctx, "metadata"):
        ctx.metadata = {}

    # Validate prerequisites
    if ctx.strategy is None:
        msg = "Strategy not available for production run"
        raise ValueError(msg)

    if telemetry_reader is None:
        msg = "Telemetry reader is required for production monitoring"
        raise ValueError(msg)

    # Start champion fuzzer
    logger.info("Starting champion fuzzer with initial strategy")
    try:
        fuzzer_id = await runtime_client.start_fuzzer(
            fuzzer_type="champion",
            strategy=ctx.strategy,
            target_path=ctx.target_path,
        )
        ctx.metadata["champion_fuzzer_id"] = fuzzer_id
        logger.info(f"Champion fuzzer started with ID: {fuzzer_id}")
    except Exception as e:
        logger.error(f"Failed to start champion fuzzer: {e}")
        ctx.metadata["phase3_error"] = str(e)
        return ctx

    # Shared state with thread-safe access
    state_analyses: list[StateAnalysis] = []
    performance_anomalies: list[str] = []
    needs_adjustment = False
    telemetry_analyzer = TelemetryAnalyzer()

    # Synchronization primitives for safe concurrent access
    data_lock = asyncio.Lock()
    stop_event = asyncio.Event()

    # Task timing control
    start_time = datetime.now(timezone.utc)
    end_time = start_time + timedelta(hours=duration_hours)

    # Create shadow process manager
    shadow_manager = ShadowProcessManager(
        runtime_client=runtime_client,
        telemetry_collector=telemetry_reader,  # Use telemetry reader as collector
        config=getattr(ctx, "config", {}),
        champion_id=ctx.metadata.get(
            "champion_fuzzer_id"
        ),  # Pass champion_id from context
    )

    # Task 1: Telemetry Collection
    async def telemetry_collection_task() -> None:
        """Continuously collect telemetry data without blocking."""
        logger.info("Telemetry collection task started")

        while not stop_event.is_set() and datetime.now(timezone.utc) < end_time:
            try:
                # Read telemetry entries in batches for efficiency
                entries = await telemetry_reader.read_batch(max_entries=1000)

                if entries:
                    async with data_lock:
                        # Process entries into aggregators
                        for entry in entries:
                            instance_id = entry.get("instance_id", "default")
                            if instance_id not in telemetry_analyzer.aggregators:
                                telemetry_analyzer.aggregators[instance_id] = (
                                    TelemetryAggregator()
                                )
                            telemetry_analyzer.aggregators[
                                instance_id
                            ].process_telemetry_entry(entry)

                # Small sleep to prevent CPU spinning
                await asyncio.sleep(0.1)

            except Exception as e:
                logger.error(f"Error in telemetry collection: {e}")
                await asyncio.sleep(1.0)  # Back off on error

        logger.info("Telemetry collection task completed")

    # Task 2: LLM Analysis (每10分钟执行)
    async def llm_analysis_task() -> None:
        """Perform periodic LLM analysis every 10 minutes as per workflow.md."""
        logger.info("LLM analysis task started")

        analysis_interval = timedelta(minutes=10)
        last_analysis = datetime.now(timezone.utc)

        # Wait initial period to collect data
        await asyncio.sleep(30)  # 30 seconds initial wait

        while not stop_event.is_set() and datetime.now(timezone.utc) < end_time:
            try:
                current_time = datetime.now(timezone.utc)

                # Check if 10 minutes have passed
                if current_time - last_analysis >= analysis_interval:
                    logger.info(
                        "Performing scheduled LLM state analysis (10-minute interval)"
                    )

                    # Collect current metrics under lock
                    metrics_list = []
                    async with data_lock:
                        for (
                            instance_id,
                            aggregator,
                        ) in telemetry_analyzer.aggregators.items():
                            metrics = aggregator.get_current_metrics()
                            metrics.instance_id = instance_id
                            metrics_list.append(metrics)

                    # Analyze each metric set with LLM
                    for metrics in metrics_list:
                        logger.info(
                            f"Sending metrics to LLM for instance: {metrics.instance_id}"
                        )

                        try:
                            analysis = await _analyze_state_with_metrics(
                                llm_client,
                                metrics,
                                ctx,
                            )

                            async with data_lock:
                                state_analyses.append(analysis)

                                if analysis.needs_adjustment:
                                    logger.warning(
                                        f"LLM detected need for adjustment: {analysis.bottlenecks}"
                                    )
                                    nonlocal needs_adjustment
                                    needs_adjustment = True

                                    # Trigger shadow process creation
                                    if analysis.suggested_actions:
                                        logger.info(
                                            "Creating shadow process with new strategy"
                                        )
                                        # Generate new strategy based on suggestions
                                        try:
                                            new_strategy = (
                                                await _generate_optimized_strategy(
                                                    llm_client, ctx, analysis, metrics
                                                )
                                            )
                                            if new_strategy:
                                                await shadow_manager.request_shadow(
                                                    reason=f"LLM adjustment: {', '.join(analysis.bottlenecks[:2])}",
                                                    new_strategy_code=new_strategy.get(
                                                        "custom_code", ""
                                                    ),
                                                    confidence=analysis.confidence,
                                                )
                                        except Exception as e:
                                            logger.error(
                                                f"Failed to create shadow process: {e}"
                                            )

                        except Exception as e:
                            logger.error(f"Error in LLM analysis: {e}")

                    last_analysis = current_time

                # Check every 30 seconds for timing
                await asyncio.sleep(30)

            except Exception as e:
                logger.error(f"Error in LLM analysis task: {e}")
                await asyncio.sleep(60)  # Back off on error

        logger.info("LLM analysis task completed")

    # Task 3: Anomaly Detection
    async def anomaly_detection_task() -> None:
        """Perform real-time anomaly detection with immediate response."""
        logger.info("Anomaly detection task started")

        window_interval = timedelta(seconds=60)  # Check every minute
        last_window_rotation = datetime.now(timezone.utc)

        while not stop_event.is_set() and datetime.now(timezone.utc) < end_time:
            try:
                current_time = datetime.now(timezone.utc)

                # Rotate windows and check for anomalies
                if current_time - last_window_rotation >= window_interval:
                    async with data_lock:
                        for (
                            instance_id,
                            aggregator,
                        ) in telemetry_analyzer.aggregators.items():
                            # Get current metrics
                            metrics = aggregator.get_current_metrics()
                            metrics.instance_id = instance_id

                            # Detect anomalies
                            anomalies = telemetry_analyzer.detect_anomalies(metrics)

                            if anomalies:
                                logger.warning(
                                    f"Anomalies detected for {instance_id}: {anomalies}"
                                )
                                performance_anomalies.extend(anomalies)

                                # Critical anomalies trigger immediate LLM analysis
                                critical_anomalies = [
                                    a
                                    for a in anomalies
                                    if "dropped significantly" in a or "stagnation" in a
                                ]

                                if critical_anomalies:
                                    logger.info(
                                        "Critical anomaly - triggering immediate LLM analysis"
                                    )

                                    try:
                                        analysis = await _analyze_state_with_metrics(
                                            llm_client,
                                            metrics,
                                            ctx,
                                            anomalies=critical_anomalies,
                                        )

                                        state_analyses.append(analysis)
                                        if analysis.needs_adjustment:
                                            nonlocal needs_adjustment
                                            needs_adjustment = True

                                            # Trigger shadow process for critical anomaly
                                            logger.info(
                                                "Creating shadow process for critical anomaly"
                                            )
                                            try:
                                                new_strategy = (
                                                    await _generate_optimized_strategy(
                                                        llm_client,
                                                        ctx,
                                                        analysis,
                                                        metrics,
                                                    )
                                                )
                                                if new_strategy:
                                                    await shadow_manager.request_shadow(
                                                        reason=f"Critical anomaly: {critical_anomalies[0]}",
                                                        new_strategy_code=new_strategy.get(
                                                            "custom_code", ""
                                                        ),
                                                        confidence=analysis.confidence,
                                                    )
                                            except Exception as e:
                                                logger.error(
                                                    f"Failed to create shadow for anomaly: {e}"
                                                )

                                    except Exception as e:
                                        logger.error(
                                            f"Error in anomaly LLM analysis: {e}"
                                        )

                            # Rotate window and save metrics
                            rotated_metrics = aggregator.rotate_window()
                            rotated_metrics.instance_id = instance_id
                            telemetry_analyzer.metrics_history.append(rotated_metrics)

                    last_window_rotation = current_time

                # Check every 10 seconds for responsiveness
                await asyncio.sleep(10)

            except Exception as e:
                logger.error(f"Error in anomaly detection task: {e}")
                await asyncio.sleep(30)  # Back off on error

        logger.info("Anomaly detection task completed")

    # Main execution block
    tasks = []
    try:
        # Start shadow process manager
        shadow_manager_task = asyncio.create_task(
            shadow_manager.start(), name="shadow_manager"
        )

        # Create concurrent tasks
        tasks = [
            asyncio.create_task(
                telemetry_collection_task(), name="telemetry_collection"
            ),
            asyncio.create_task(llm_analysis_task(), name="llm_analysis"),
            asyncio.create_task(anomaly_detection_task(), name="anomaly_detection"),
            shadow_manager_task,
        ]

        logger.info(f"Started {len(tasks)} concurrent monitoring tasks")

        # Event-driven main loop instead of blocking sleep
        check_interval = 60  # Check every minute
        elapsed_time = 0

        while elapsed_time < duration_hours * 3600 and not stop_event.is_set():
            # Wait for the check interval
            await asyncio.sleep(check_interval)
            elapsed_time += check_interval

            # Log progress
            progress = (elapsed_time / (duration_hours * 3600)) * 100
            logger.info(
                f"Production run progress: {progress:.1f}% ({elapsed_time/3600:.1f}/{duration_hours} hours)"
            )

            # Check if any task has failed
            for task in tasks:
                if task.done() and not task.cancelled():
                    try:
                        task.result()  # This will raise any exception
                    except Exception as e:
                        logger.error(f"Task {task.get_name()} failed: {e}")
                        # Optionally restart the task or handle the error

            # Update shadow process status in metadata
            ctx.metadata["shadow_process_status"] = shadow_manager.get_status()

        logger.info("Production run duration reached, stopping tasks...")

    except Exception as e:
        logger.exception("Error during production monitoring: %s", e)
        ctx.metadata["phase3_monitoring_error"] = str(e)

    finally:
        # Graceful shutdown
        stop_event.set()

        # Stop shadow manager first
        try:
            await shadow_manager.stop()
        except Exception as e:
            logger.error(f"Error stopping shadow manager: {e}")

        # Cancel all tasks
        for task in tasks:
            if not task.done():
                task.cancel()

        # Wait for tasks to complete with timeout
        if tasks:
            try:
                await asyncio.wait_for(
                    asyncio.gather(*tasks, return_exceptions=True), timeout=5.0
                )
            except asyncio.TimeoutError:
                logger.warning("Some tasks did not complete gracefully")

        # Stop champion fuzzer
        try:
            logger.info("Stopping champion fuzzer")
            await runtime_client.stop_fuzzer(fuzzer_id)
        except Exception as e:
            logger.exception("Failed to stop fuzzer: %s", e)

    # Generate final report
    performance_report = telemetry_analyzer.generate_performance_report()

    # Update context with comprehensive results
    ctx.metadata.update(
        {
            "phase3_metrics_history": [
                m.__dict__ for m in telemetry_analyzer.metrics_history
            ],
            "phase3_state_analyses": [
                {
                    "timestamp": a.timestamp.isoformat(),
                    "stage": a.current_stage,
                    "confidence": a.confidence,
                    "bottlenecks": a.bottlenecks,
                    "needs_adjustment": a.needs_adjustment,
                    "suggested_actions": a.suggested_actions,
                }
                for a in state_analyses
            ],
            "phase3_needs_adjustment": needs_adjustment,
            "phase3_performance_report": performance_report,
            "phase3_anomalies": performance_anomalies,
            "phase3_duration_hours": duration_hours,
            "phase3_architecture": "concurrent_tasks_with_shadow_processes",
            "phase3_shadow_processes": shadow_manager.get_status(),
            "phase3_completed": datetime.now(timezone.utc).isoformat(),
        },
    )

    logger.info(
        f"Phase 3 completed. Collected {len(telemetry_analyzer.metrics_history)} metric windows. "
        f"State analyses: {len(state_analyses)}. Needs adjustment: {needs_adjustment}"
    )

    return ctx


async def _analyze_state_with_metrics(
    llm_client: LiteLLMClient,
    metrics: PerformanceMetrics,
    ctx: CampaignContext,
    anomalies: list[str] | None = None,
) -> StateAnalysis:
    """Analyze fuzzing state using metrics with LLM.

    Args:
        llm_client: LLM client for analysis
        metrics: Current performance metrics
        ctx: Campaign context
        anomalies: Optional list of detected anomalies

    Returns:
        State analysis from LLM
    """
    try:
        # Prepare metrics summary
        metrics_summary = f"""Performance Metrics Analysis:
- Instance: {metrics.instance_id}
- Total Executions: {metrics.total_executions:,}
- Execution Rate: {metrics.execution_rate:.1f} exec/s
- Coverage Hits: {metrics.coverage_hits:,}
- Unique Edges: {metrics.unique_edges:,}
- Path Discovery Rate: {metrics.path_discovery_rate:.2f} paths/min
- Crashes Found: {metrics.crashes_found}
- Crash Rate: {metrics.crash_rate:.2f} crashes/1M execs
- Corpus Size: {metrics.corpus_size}
- Corpus Growth Rate: {metrics.corpus_growth_rate:.2f} inputs/min
- Efficiency Score: {metrics.efficiency_score:.3f}"""

        if anomalies:
            metrics_summary += "\n\nDetected Anomalies:\n" + "\n".join(
                f"- {a}" for a in anomalies
            )

        # Generate prompt
        try:
            prompt = _prompt_manager.get_prompt(
                "analysis.state_analysis",
                target_path=ctx.target_path,
                strategy_name=(
                    ctx.strategy.get("name", "unknown") if ctx.strategy else "unknown"
                ),
                elapsed_hours=getattr(ctx, "elapsed_hours", 0.0),
                telemetry_summary=metrics_summary,
            )
        except Exception as e:
            logger.error(f"Failed to generate prompt: {e}")
            # Use fallback prompt template
            try:
                prompt = _prompt_manager.get_prompt(
                    "analysis.state_analysis_fallback",
                    target_path=ctx.target_path,
                    strategy_name=(
                        ctx.strategy.get("name", "unknown")
                        if ctx.strategy
                        else "unknown"
                    ),
                    telemetry_summary=metrics_summary,
                )
            except Exception as fallback_error:
                logger.error(f"Failed to generate fallback prompt: {fallback_error}")
                # Last resort: minimal prompt
                prompt = f"Analyze fuzzing state for {ctx.target_path}. Metrics: {metrics_summary[:200]}... Respond with JSON: current_stage, confidence, bottlenecks, needs_adjustment, suggested_actions"

        # Get LLM analysis
        response_text = llm_client.generate(
            prompt=prompt,
            max_tokens=500,
            temperature=0.3,
        )

        # Parse response
        import json_repair

        analysis_data = json_repair.loads(response_text)

        # Ensure valid response
        if not isinstance(analysis_data, dict):
            analysis_data = {}

        return StateAnalysis(
            timestamp=datetime.now(timezone.utc),
            current_stage=str(analysis_data.get("current_stage", "unknown")),
            confidence=float(analysis_data.get("confidence", 0.5)),
            bottlenecks=list(analysis_data.get("bottlenecks", [])),
            needs_adjustment=bool(analysis_data.get("needs_adjustment", False)),
            suggested_actions=list(analysis_data.get("suggested_actions", [])),
            raw_response=response_text,
        )

    except Exception as e:
        logger.exception("Failed to analyze state with LLM: %s", e)
        # Return minimal analysis on failure
        return StateAnalysis(
            timestamp=datetime.now(timezone.utc),
            current_stage="unknown",
            confidence=0.0,
            bottlenecks=[f"Analysis failed: {str(e)}"],
            needs_adjustment=False,
        )


async def _generate_optimized_strategy(
    llm_client: LiteLLMClient,
    ctx: CampaignContext,
    analysis: StateAnalysis,
    metrics: PerformanceMetrics,
) -> dict | None:
    """Generate an optimized strategy based on analysis and current metrics.

    Args:
        llm_client: LLM client for strategy generation
        ctx: Campaign context with current strategy
        analysis: State analysis with bottlenecks and suggestions
        metrics: Current performance metrics

    Returns:
        New strategy dictionary or None if generation fails
    """
    try:
        # Prepare context for strategy optimization
        optimization_context = f"""Current Strategy Analysis:
- Stage: {analysis.current_stage}
- Bottlenecks: {', '.join(analysis.bottlenecks)}
- Suggested Actions: {', '.join(analysis.suggested_actions)}

Performance Metrics:
- Coverage: {metrics.coverage_hits:,} hits
- Execution Rate: {metrics.execution_rate:.1f} exec/s
- Path Discovery: {metrics.path_discovery_rate:.2f} paths/min
- Crashes: {metrics.crashes_found}
- Efficiency Score: {metrics.efficiency_score:.3f}

Target: {ctx.target_path}
Current Strategy: {ctx.strategy.get('name', 'unknown') if ctx.strategy else 'unknown'}"""

        # Generate optimization prompt
        prompt = _prompt_manager.get_prompt(
            "strategy_optimization",
            context=optimization_context,
            current_strategy=ctx.strategy,
        )

        # Get LLM to generate optimized strategy
        response = llm_client.generate(
            prompt=prompt,
            max_tokens=1000,
            temperature=0.7,
        )

        # Parse response
        import json_repair

        strategy_data = json_repair.loads(response)

        if not isinstance(strategy_data, dict):
            logger.error("Invalid strategy response from LLM")
            return None

        # Ensure required fields
        if "custom_code" not in strategy_data:
            logger.error("Generated strategy missing custom_code")
            return None

        # Add metadata
        strategy_data["name"] = (
            f"shadow_strategy_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}"
        )
        strategy_data["parent"] = (
            ctx.strategy.get("name", "unknown") if ctx.strategy else "unknown"
        )
        strategy_data["optimization_reason"] = (
            analysis.bottlenecks[0] if analysis.bottlenecks else "performance"
        )

        logger.info(f"Generated optimized strategy: {strategy_data['name']}")
        return strategy_data

    except Exception as e:
        logger.error(f"Failed to generate optimized strategy: {e}")
        return None
