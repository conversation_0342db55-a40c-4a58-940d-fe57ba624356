"""
Shadow Process Manager - Dynamic shadow process creation and management for FuzzLM-Agent

This module implements the SOTA (State-of-the-art) dynamic shadow process management
as described in docs/workflow.md. It provides real-time shadow process creation,
monitoring, and promotion during Phase 3 production runs.
"""

import asyncio
import logging
import time
from collections import deque
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Optional

from fuzzlm_agent.infrastructure.runtime_client import RuntimeClient

logger = logging.getLogger(__name__)


class ShadowProcessState(Enum):
    """Shadow process lifecycle states"""

    CREATED = "created"
    RUNNING = "running"
    EVALUATING = "evaluating"
    PROMOTED = "promoted"
    TERMINATED = "terminated"
    FAILED = "failed"


@dataclass
class ShadowProcessEvent:
    """Base class for shadow process events"""

    timestamp: float = field(default_factory=time.time)
    event_type: str = "base_event"


@dataclass
class CreateShadowEvent(ShadowProcessEvent):
    """Event to trigger shadow process creation"""

    event_type: str = "create_shadow"
    reason: str = ""
    new_strategy_code: str = ""
    confidence_score: float = 0.0


@dataclass
class PromoteShadowEvent(ShadowProcessEvent):
    """Event to trigger shadow process promotion"""

    event_type: str = "promote_shadow"
    shadow_id: str = ""
    performance_gain: float = 0.0


@dataclass
class TerminateShadowEvent(ShadowProcessEvent):
    """Event to terminate a shadow process"""

    event_type: str = "terminate_shadow"
    shadow_id: str = ""
    reason: str = ""


@dataclass
class PerformanceMetrics:
    """Performance metrics for a fuzzer instance"""

    coverage: float = 0.0
    crashes_found: int = 0
    exec_per_sec: float = 0.0
    corpus_size: int = 0
    timestamp: float = field(default_factory=time.time)


@dataclass
class ShadowProcess:
    """Shadow process information and state"""

    id: str
    state: ShadowProcessState = ShadowProcessState.CREATED
    created_at: float = field(default_factory=time.time)
    strategy_code: str = ""
    performance_history: deque[PerformanceMetrics] = field(
        default_factory=lambda: deque(maxlen=10)
    )
    promotion_score: float = 0.0
    resource_limit: float = 0.2  # 20% CPU by default


class ShadowProcessManager:
    """
    Manages shadow process lifecycle during Phase 3 production runs.

    Key responsibilities:
    1. Monitor events for shadow process creation triggers
    2. Create and manage shadow processes with resource constraints
    3. Monitor shadow process performance in real-time
    4. Make promotion decisions based on performance comparison
    5. Handle shadow process termination and cleanup
    """

    def __init__(
        self,
        runtime_client: RuntimeClient,
        telemetry_collector: Any,  # Can be TelemetryReader or similar
        config: dict[str, Any],
        champion_id: str | None = None,  # Add champion_id parameter
    ):
        self.runtime_client = runtime_client
        self.telemetry = telemetry_collector
        self.config = config
        self.champion_id = champion_id  # Store champion_id

        # Event queue for inter-component communication
        self.event_queue: asyncio.Queue[ShadowProcessEvent] = asyncio.Queue()

        # Active shadow processes
        self.shadow_processes: dict[str, ShadowProcess] = {}

        # Champion performance tracking
        self.champion_metrics: deque[PerformanceMetrics] = deque(maxlen=10)

        # Configuration
        self.max_shadows = config.get("shadow", {}).get("max_concurrent", 3)
        self.performance_window = config.get("shadow", {}).get(
            "performance_window", 300
        )  # 5 minutes
        self.promotion_threshold = config.get("shadow", {}).get(
            "promotion_threshold", 0.1
        )  # 10% improvement
        self.shadow_timeout = config.get("shadow", {}).get("timeout_hours", 1.0)

        # State
        self.running = False

    async def start(self) -> None:
        """Start the shadow process manager"""
        self.running = True
        logger.info("Shadow Process Manager started")

        # Start background tasks
        tasks = [
            self._event_processor(),
            self._performance_monitor(),
            self._lifecycle_manager(),
        ]

        await asyncio.gather(*tasks)

    async def stop(self) -> None:
        """Stop the shadow process manager"""
        self.running = False

        # Terminate all shadow processes
        for shadow_id in list(self.shadow_processes.keys()):
            await self._terminate_shadow(shadow_id, "Manager shutdown")

        logger.info("Shadow Process Manager stopped")

    async def request_shadow(
        self, reason: str, new_strategy_code: str, confidence: float
    ) -> None:
        """Request creation of a new shadow process"""
        event = CreateShadowEvent(
            reason=reason,
            new_strategy_code=new_strategy_code,
            confidence_score=confidence,
        )
        await self.event_queue.put(event)
        logger.info(
            f"Shadow process requested: {reason} (confidence: {confidence:.2f})"
        )

    async def _event_processor(self) -> None:
        """Process events from the event queue"""
        while self.running:
            try:
                # Wait for event with timeout
                event = await asyncio.wait_for(self.event_queue.get(), timeout=1.0)

                if isinstance(event, CreateShadowEvent):
                    await self._handle_create_shadow(event)
                elif isinstance(event, PromoteShadowEvent):
                    await self._handle_promote_shadow(event)
                elif isinstance(event, TerminateShadowEvent):
                    await self._handle_terminate_shadow(event)

            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"Error processing event: {e}")

    async def _handle_create_shadow(self, event: CreateShadowEvent) -> None:
        """Handle shadow process creation request"""
        # Check if we can create more shadows
        if len(self.shadow_processes) >= self.max_shadows:
            logger.warning(f"Maximum shadow processes ({self.max_shadows}) reached")
            return

        try:
            # Create shadow process via gRPC
            # Use the champion_id passed from Phase 3
            champion_id = self.champion_id
            if not champion_id:
                logger.error("No champion_id available for shadow creation")
                return

            # Create shadow strategy with resource limits
            shadow_strategy = {
                "name": f"shadow_{int(time.time())}",
                "custom_code": event.new_strategy_code,
                "resource_limit": self.config.get("shadow", {}).get(
                    "resource_limit", 0.2
                ),
            }

            shadow_id = await self.runtime_client.spawn_shadow(
                champion_id=champion_id, shadow_strategy=shadow_strategy
            )

            if shadow_id:
                shadow = ShadowProcess(
                    id=shadow_id,
                    state=ShadowProcessState.RUNNING,
                    strategy_code=event.new_strategy_code,
                )
                self.shadow_processes[shadow_id] = shadow

                logger.info(f"Shadow process created: {shadow.id}")
            else:
                logger.error("Failed to create shadow process: no ID returned")

        except Exception as e:
            logger.error(f"Error creating shadow process: {e}")

    async def _handle_promote_shadow(self, event: PromoteShadowEvent) -> None:
        """Handle shadow process promotion"""
        shadow = self.shadow_processes.get(event.shadow_id)
        if not shadow:
            logger.error(f"Shadow process not found: {event.shadow_id}")
            return

        try:
            # Promote shadow to champion
            success = await self.runtime_client.promote_shadow(event.shadow_id)

            if success:
                shadow.state = ShadowProcessState.PROMOTED
                logger.info(
                    f"Shadow process promoted: {event.shadow_id} (gain: {event.performance_gain:.2%})"
                )

                # Clean up promoted shadow
                del self.shadow_processes[event.shadow_id]
            else:
                logger.error(f"Failed to promote shadow: {event.shadow_id}")

        except Exception as e:
            logger.error(f"Error promoting shadow process: {e}")

    async def _handle_terminate_shadow(self, event: TerminateShadowEvent) -> None:
        """Handle shadow process termination"""
        await self._terminate_shadow(event.shadow_id, event.reason)

    async def _terminate_shadow(self, shadow_id: str, reason: str) -> None:
        """Terminate a shadow process"""
        shadow = self.shadow_processes.get(shadow_id)
        if not shadow:
            return

        try:
            # Stop the shadow process
            await self.runtime_client.stop_fuzzer(shadow_id)

            shadow.state = ShadowProcessState.TERMINATED
            del self.shadow_processes[shadow_id]

            logger.info(f"Shadow process terminated: {shadow_id} ({reason})")

        except Exception as e:
            logger.error(f"Error terminating shadow process: {e}")
            shadow.state = ShadowProcessState.FAILED

    async def _performance_monitor(self) -> None:
        """Monitor performance of champion and shadow processes"""
        while self.running:
            try:
                # Collect champion metrics
                if self.champion_id:
                    champion_stats = await self._collect_performance_metrics(
                        self.champion_id
                    )
                    if champion_stats:
                        self.champion_metrics.append(champion_stats)

                # Collect shadow metrics and evaluate
                for shadow_id, shadow in list(self.shadow_processes.items()):
                    if shadow.state != ShadowProcessState.RUNNING:
                        continue

                    shadow_stats = await self._collect_performance_metrics(shadow_id)
                    if shadow_stats:
                        shadow.performance_history.append(shadow_stats)

                        # Evaluate promotion
                        if len(shadow.performance_history) >= 3:
                            await self._evaluate_shadow_promotion(shadow_id)

                # Wait before next collection
                await asyncio.sleep(30)  # Collect every 30 seconds

            except Exception as e:
                logger.error(f"Error in performance monitor: {e}")
                await asyncio.sleep(30)

    async def _collect_performance_metrics(
        self, fuzzer_id: str
    ) -> Optional[PerformanceMetrics]:
        """Collect performance metrics for a specific fuzzer"""
        try:
            # Get latest telemetry data
            # Use read_batch method which is available in TelemetryReader
            if hasattr(self.telemetry, "read_batch"):
                latest_data = await self.telemetry.read_batch(max_entries=1000)
            elif hasattr(self.telemetry, "get_latest_data"):
                latest_data = self.telemetry.get_latest_data()
            else:
                logger.warning("Telemetry collector doesn't have expected interface")
                return None

            # Extract metrics for the specific fuzzer
            metrics = PerformanceMetrics()

            # Find relevant telemetry entries
            for entry in latest_data:
                if entry.get("fuzzer_id") == fuzzer_id or (
                    fuzzer_id == self.champion_id and not entry.get("fuzzer_id")
                ):
                    entry_type = entry.get("type")
                    data = entry.get("data", {})

                    if entry_type == "ExecutionStats":
                        metrics.exec_per_sec = data.get("exec_per_sec", 0)
                    elif entry_type == "CoverageUpdate":
                        metrics.coverage = data.get("total_coverage", 0)
                    elif entry_type == "CrashFound":
                        metrics.crashes_found += 1
                    elif entry_type == "CorpusUpdate":
                        metrics.corpus_size = data.get("corpus_size", 0)

            return metrics

        except Exception as e:
            logger.error(f"Error collecting metrics for {fuzzer_id}: {e}")
            return None

    async def _evaluate_shadow_promotion(self, shadow_id: str) -> None:
        """Evaluate if a shadow process should be promoted"""
        shadow = self.shadow_processes.get(shadow_id)
        if not shadow or shadow.state != ShadowProcessState.RUNNING:
            return

        # Calculate performance score
        shadow_score = self._calculate_performance_score(shadow.performance_history)
        champion_score = self._calculate_performance_score(self.champion_metrics)

        if champion_score > 0:
            performance_gain = (shadow_score - champion_score) / champion_score
            shadow.promotion_score = performance_gain

            # Check if shadow consistently outperforms champion
            if performance_gain > self.promotion_threshold:
                logger.info(
                    f"Shadow {shadow_id} outperforms champion by {performance_gain:.2%}"
                )

                # Create promotion event
                event = PromoteShadowEvent(
                    shadow_id=shadow_id, performance_gain=performance_gain
                )
                await self.event_queue.put(event)

    def _calculate_performance_score(self, metrics: deque[PerformanceMetrics]) -> float:
        """Calculate weighted performance score from metrics history"""
        if not metrics:
            return 0.0

        # Weights for different metrics
        weights = {"coverage": 0.4, "crashes": 0.3, "exec_speed": 0.2, "corpus": 0.1}

        # Calculate weighted average with time decay
        total_score = 0.0
        total_weight = 0.0

        for i, metric in enumerate(metrics):
            # Time decay factor (more recent = higher weight)
            time_weight = 1.0 - (0.1 * (len(metrics) - 1 - i))

            # Calculate metric score
            score = (
                metric.coverage * weights["coverage"]
                + min(metric.crashes_found / 10, 1.0)
                * weights["crashes"]  # Normalize crashes
                + min(metric.exec_per_sec / 10000, 1.0)
                * weights["exec_speed"]  # Normalize exec/s
                + min(metric.corpus_size / 1000, 1.0)
                * weights["corpus"]  # Normalize corpus
            )

            total_score += score * time_weight
            total_weight += time_weight

        return total_score / total_weight if total_weight > 0 else 0.0

    async def _lifecycle_manager(self) -> None:
        """Manage shadow process lifecycle (timeouts, resource limits, etc.)"""
        while self.running:
            try:
                current_time = time.time()

                for shadow_id, shadow in list(self.shadow_processes.items()):
                    # Check timeout
                    if current_time - shadow.created_at > self.shadow_timeout * 3600:
                        logger.info(f"Shadow {shadow_id} timed out")
                        await self._terminate_shadow(shadow_id, "Timeout")
                        continue

                    # Check if shadow is underperforming
                    if (
                        len(shadow.performance_history) >= 5
                        and shadow.promotion_score < -0.1
                    ):
                        logger.info(f"Shadow {shadow_id} underperforming")
                        await self._terminate_shadow(shadow_id, "Underperforming")

                # Wait before next check
                await asyncio.sleep(60)  # Check every minute

            except Exception as e:
                logger.error(f"Error in lifecycle manager: {e}")
                await asyncio.sleep(60)

    def get_status(self) -> dict[str, Any]:
        """Get current status of shadow processes"""
        return {
            "active_shadows": len(self.shadow_processes),
            "shadows": {
                shadow_id: {
                    "state": shadow.state.value,
                    "created_at": datetime.fromtimestamp(shadow.created_at).isoformat(),
                    "promotion_score": shadow.promotion_score,
                    "performance_history_length": len(shadow.performance_history),
                }
                for shadow_id, shadow in self.shadow_processes.items()
            },
        }
